@using System.Diagnostics.CodeAnalysis;
@using Microsoft.AspNetCore.Components;
@using Newtonsoft.Json
@using System.Reflection
@using TEX.Model.Models;
@using TEX.ViewModel.Finished.ProductInboundLotVMs
@using TEX.ViewModel.Finished.ProductInboundRollVMs
@using TEX.ViewModel.Producttion.LotAllocateVMs

@inherits ComponentBase;

<div style="white-space: nowrap;">
    <div class="@parentTableClass">
        <Table TItem="ProductInboundLot" @bind-Items="@DetailList" ShowRefresh="false" EditDialogSize="Size.Medium"
               IsPagination="false" TableSize="TableSize.Compact" ShowSkeleton="true"   IsExcel="true"
               OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" IsTracking="true"
               OnDeleteAsync="@OnDeleteAsync" ShowToastAfterSaveOrDeleteModel="false"
               ShowEditButton="false" ShowDeleteButton="false" IsFixedHeader="true"
               IsMultipleSelect="false" ShowAddButton="@isAdd"
               ShowLineNo="true" LineNoText="序号" class="footer-demo"
               ShowToolbar="true" ShowExtendButtons="true" IsBordered="true"
               ShowFooter="true" IsFixedFooter="true" IsHideFooterWhenNoData="true" Height="360" style="margin:16px 0;">
            <TableColumns>
                <TableColumn @bind-Field="@context.OrderDetailId" Text="@WtmBlazor.Localizer["_Color"]" Lookup="AllOrderDetails" />
                <TableColumn @bind-Field="@context.LotNo" />
                <TableColumn @bind-Field="@context.Pcs" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput Value="@v.Pcs" FormatString="0" IsSelectAllTextOnFocus OnEnterAsync="@OnEnterAsync"/>
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Weight" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput Value="@v.Weight" FormatString="0.##" IsSelectAllTextOnFocus />
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Meters" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput Value="@v.Meters" FormatString="0.##" IsSelectAllTextOnFocus />
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Yards" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput Value="@v.Yards" FormatString="0.##" IsSelectAllTextOnFocus />
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Location" />
                <TableColumn @bind-Field="@context.Remark" />
            </TableColumns>
            <TableToolbarTemplate>
                @* <TableToolbarButton TItem="ProductInboundLot_View" Color="Color.Primary" Icon="fa-fw fa-solid fa-pen-to-square" Text="选择" OnClick="@OnSelectLotAsync" /> *@
            </TableToolbarTemplate>
            <RowButtonTemplate>
                <TableCellButton Size="Size.ExtraSmall" Icon="fa fa-info-circle" Color="Color.Info" Text="明细" OnClick="()=>OnDetailsClick(context)" />
            </RowButtonTemplate>

            <FooterTemplate>
                <TableFooterCell />
                <TableFooterCell Text="合计:" style="height: 36px;"/>
                <TableFooterCell Aggregate="AggregateType.Count" Field="@nameof(ProductInboundLot_View.LotNo)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundLot_View.Pcs)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundLot_View.Meters)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundLot_View.Yards)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundLot_View.Weight)" />
                <TableFooterCell Colspan="3" />
            </FooterTemplate>


        </Table>
    </div>
    <div class="@childTableClass">

        <Table @bind-Items="rollDetailList" TItem="ProductInboundRoll"
               IsExcel="true" OnAddAsync="@OnAddRollAsync" OnSaveAsync="@OnSaveRollAsync" OnDeleteAsync="@OnDeleteRollAsync"
               ShowToolbar="true" ShowDeleteButton="true" ShowRefresh="false"
               ShowExtendButtons="false" 
               ShowToastAfterSaveOrDeleteModel="false" TableSize="TableSize.Compact"  
               IsFixedHeader="true" Height="360" IsBordered="true"
               ShowFooter="true" IsFixedFooter="true" IsHideFooterWhenNoData="true">
            <TableColumns>
                <TableColumn @bind-Field="@context.RollNo" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.RollNo" FormatString="0" IsSelectAllTextOnFocus />
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Meters" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Meters" FormatString="0.##" IsSelectAllTextOnFocus />
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Yards" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Yards" FormatString="0.##" IsSelectAllTextOnFocus />
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Weight" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Weight" FormatString="0.##" IsSelectAllTextOnFocus />
                    </EditTemplate>
                </TableColumn>
 
                @* <TableColumn @bind-Field="@context.Meters" />
                <TableColumn @bind-Field="@context.Yards" />
                <TableColumn @bind-Field="@context.Weight" /> *@
                <TableColumn @bind-Field="@context.Remark" />
            </TableColumns>
            <TableToolbarTemplate>
                <TableToolbarButton Color="Color.Warning" Icon="fa fa-info-save" Text="保存" OnClick="@SaveRolls" />
            </TableToolbarTemplate> 
            <FooterTemplate>
                @* <TableFooterCell Text="合计:" style="height: 36px;" /> *@
                <TableFooterCell style="height: 36px;" Text="合计:" Aggregate="AggregateType.Count" Field="@nameof(ProductInboundRoll.RollNo)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundRoll.Meters)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundRoll.Weight)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundRoll.Yards)" />
                <TableFooterCell  />
            </FooterTemplate>
        </Table>

    </div>
</div>
<style>
    .parent-table-normal {
        width: 100%;
        display: inline-block;
    }

    .parent-table-compressed {
        width: 60%;
        display: inline-block;
    }

    .child-table-hidden {
        display: none;
    }

    .child-table-visible {
        width: 40%;
        display: inline-block;
    }

    .footer-demo hr {
        margin: 0;
    }

    .footer-demo tfoot tr,
    .footer-demo .table-row.table-footer .table-cell {
        color: #409eff;
        font-weight: bold;
    }
</style>
@code {

    [Parameter]
    public ProductInboundBill Bill { get; set; } = new();
    [Parameter]
    public EventCallback<ProductInboundBill> BillChanged { get; set; }

    [Inject]
    private WtmBlazorContext WtmBlazor { get; set; }

    private List<SelectedItem> AllOrderDetails { get; set; } = new List<SelectedItem>();
    bool isRollTableDisplay = false;//是否显示Roll表格
    ProductInboundLot SelectedLot { get; set; }//存储当前选择的Lot
    private bool isAdd = false;
    //Lot子表绑定数据
    public IEnumerable<ProductInboundLot> DetailList
    {
        get { return Bill.LotList; }
        set
        {
            Bill.LotList = value.ToList();
        }
    }

    //Roll孙表绑定数据
    List<ProductInboundRoll> RollDetailList = new List<ProductInboundRoll>();
    public IEnumerable<ProductInboundRoll> rollDetailList
    {
        get { return RollDetailList; }
        set
        {
            RollDetailList = value.ToList();
        }
    }


    protected override async Task OnInitializedAsync()
    {
        if (Bill.LotList is null) Bill.LotList = new List<ProductInboundLot>();

        await base.OnInitializedAsync();
    }

    //当选择订单后,刷新订单明细选择数据源
    protected override async Task OnParametersSetAsync()
    {
        if (Bill.POrderId != Guid.Empty)
        {
            await SearchOrderDetailAsync(Bill.POrderId);
            isAdd = true;
        }
    }

    private async Task SearchOrderDetailAsync(Guid id)
    {
        var rv = await WtmBlazor.Api.CallAPI<List<SelectedItem>>($"/api/Models/OrderDetail/GetOrderDetailSelectListItemsByPurchaseOrderId/{id}");
        AllOrderDetails = rv.Data;
    }



    #region 更新Lot
    //Lot子表Excel模式,更新方法
    private async Task<ProductInboundLot> OnAddAsync()
    {
        var od = new ProductInboundLot();
        if (Bill.LotList is null) Bill.LotList = new();
        od.ID = Guid.NewGuid();
        Bill.LotList.Insert(Bill.LotList.Count(), od);
        //有async关键字就需要加await
        return await Task.FromResult(od);
        //return null;
    }

    private Task<bool> OnSaveAsync(ProductInboundLot item, ItemChangedType changedType)
    {
        Bill.Pcs = Bill.LotList.Count;
        Bill.Weight = Bill.LotList.Sum(x => x.Weight);
        Bill.Meters = Bill.LotList.Sum(x => x.Meters);
        Bill.Yards = Bill.LotList.Sum(x => x.Yards);
        return Task.FromResult(true);
    }

    private Task<bool> OnDeleteAsync(IEnumerable<ProductInboundLot> items)
    {
        Bill.LotList.RemoveAll(i => items.ToList().Contains(i));
        // 输出日志信息
        //Logger.Log($"集合值变化通知 列: {Items.Count} - 类型: Delete");
        return Task.FromResult(true);
    }
    #endregion


    #region 更新Roll
    //更新Roll
    private async Task<ProductInboundRoll> OnAddRollAsync()
    {
        var od = new ProductInboundRoll();
        if (RollDetailList is null) RollDetailList = new();
        od.ID = Guid.NewGuid();
        od.RollNo = RollDetailList.Count + 1;
        RollDetailList.Insert(RollDetailList.Count(), od);
        //有async关键字就需要加await
        return await Task.FromResult(od);
        //return null;
    }

    private Task<bool> OnSaveRollAsync(ProductInboundRoll item, ItemChangedType changedType)
    {
        // Bill.Pcs = RollDetailList.Count();
        // Bill.Weight = RollDetailList.Sum(x => x.Weight);
        // Bill.Meters = RollDetailList.Sum(x => x.Meters);
        // Bill.Yards = RollDetailList.Sum(x => x.Yards);
        return Task.FromResult(true);
    }

    private Task<bool> OnDeleteRollAsync(IEnumerable<ProductInboundRoll> items)
    {
        RollDetailList.RemoveAll(i => items.ToList().Contains(i));
        return Task.FromResult(true);
    }
    private string parentTableClass = "parent-table-normal";
    private string childTableClass = "child-table-hidden";
    //行明细按钮点击控制显示Roll表格
    private void OnDetailsClick(ProductInboundLot item)
    {

        SelectedLot = item;
        RollDetailList = item.RollList ?? new List<ProductInboundRoll>();

        if (SelectedLot is not null && SelectedLot.ID == item.ID)
        {
            //点击时,切换显示状态
            parentTableClass = parentTableClass == "parent-table-compressed" ? "parent-table-normal" : "parent-table-compressed";
            childTableClass = childTableClass == "child-table-visible" ? "child-table-hidden" : "child-table-visible";

        }
        else
        {
            SelectedLot = item;

            parentTableClass = "parent-table-compressed";
            childTableClass = "child-table-visible";
        }
        StateHasChanged();
    }

    //保存Roll
    private async Task SaveRolls()
    {
        var list = new List<ProductInboundRoll>();

        //去除空白Roll
        foreach (var item in RollDetailList)
        {
            if (item.Meters != 0 || item.Weight != 0 || item.Yards != 0)
            {
                list.Add(item);
            }
        }
        SelectedLot.RollList = list;
        SelectedLot.Color = AllOrderDetails.FirstOrDefault(x => x.Value == SelectedLot.OrderDetailId.ToString())?.Text;
        SelectedLot.Pcs = SelectedLot.RollList.Count;
        SelectedLot.Weight = SelectedLot.RollList.Sum(x => x.Weight);
        SelectedLot.Meters = SelectedLot.RollList.Sum(x => x.Meters);
        SelectedLot.Yards = SelectedLot.RollList.Sum(x => x.Yards);
        int index = Bill.LotList.FindIndex(x => x.ID == SelectedLot.ID);
        Bill.LotList[index] = SelectedLot;
        isRollTableDisplay = false;
        parentTableClass = "parent-table-normal";
        childTableClass = "child-table-hidden";
        await Task.CompletedTask;
        //StateHasChanged(); //Click事件是EventCallback类型,本身会触发StateHasChanged，所以不需要再次触发
    }
    #endregion

    private Task OnEnterAsync(int val)
    {
        //激活下一行的同位置

        return Task.CompletedTask;
    }
}
@page "/Finished/ProductInboundBill/Create"
@using System.Reflection
@using Console=System.Console
@using TEX.Model.Models
@using TEX.Shared.Pages.Components
@using TEX.ViewModel.Finished.ProductInboundBillVMs;
@using TEX.ViewModel.Finished.ProductInboundLotVMs
@using TEX.ViewModel.Models.OrderDetailVMs
@using TEX.ViewModel.Models.PurchaseOrderVMs
@using TEX.ViewModel.Producttion.LotAllocateVMs
@inherits BasePage
@inject NavigationManager Navigation

<ValidateForm @ref="vform" Model="@Model" >
    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Normal">
        <SelectOrderTable @bind-Value="@porder"
                          OnSelectedChanged="OnSelectedChanged" />
        <BootstrapInput @bind-Value="@product.ProductName" DisplayText="@WtmBlazor.Localizer["_Product"]" IsDisabled="true" />

        <DateTimePicker @bind-Value="@Model.Entity.CreateDate" />
        <BootstrapInput @bind-Value="@Model.Entity.BillNo" DisplayText="入库单号" IsDisabled="true" />
        <Select @bind-Value="@Model.Entity.FinishingFactoryId" Items="@AllFinishingFactory" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
        <BootstrapInput @bind-Value="@spec" DisplayText="@WtmBlazor.Localizer["_Spec"]" IsDisabled="true" />
        <BootstrapInput @bind-Value="@Model.Entity.Warehouse" />
        <BootstrapInput @bind-Value="@Model.Entity.Remark" />
    </Row>

    <InboundEditTemplate @bind-Bill="Model.Entity" />

    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" OnClick="Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Submit"]" IsAsync="true" />
    </div>

</ValidateForm>

@code {

    private ProductInboundBillVM Model = new ProductInboundBillVM();
    private ValidateForm vform { get; set; }
    private ProductInboundLot selectLot = new();

    // 防止重复关闭的标志
    private bool isClosing = false;

    [CascadingParameter]
    [NotNull]
    public Tab? TabSet { get; set; }


    //订单选择后查询产品名称和规格
    private Product product { get; set; } = new();
    private string spec = "";

    public PurchaseOrder_View porder { get; set; } = new();

    private List<SelectedItem> AllFinishingFactory = new List<SelectedItem>();

    protected override async Task OnInitializedAsync()
    {
        AllFinishingFactory = await WtmBlazor.Api.CallItemsApi("/api/Models/Company/GetFinishingFactorys", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        await base.OnInitializedAsync();
    }

    private async Task Submit()
    {
        //染整厂必填验证失效了,只能自定义验证了
        if (Model.Entity.FinishingFactoryId == Guid.Empty)
        {
            vform.SetError<ProductInboundBill>(f => f.FinishingFactoryId, "染整厂为必填项");
            return;
        }
        if (Model.Entity.LotList.Any())
        {
            Model.Entity.Pcs = Model.Entity.LotList.Sum(x => x.Pcs);
            Model.Entity.Meters = Model.Entity.LotList.Sum(x => x.Meters);
            Model.Entity.Yards = Model.Entity.LotList.Sum(x => x.Yards);
            Model.Entity.Weight = Model.Entity.LotList.Sum(x => x.Weight);
        }
        // 使用自定义的提交逻辑来支持异步操作
        var success = await SubmitWithCustomHandling();

        // bool s = false;
        // var rv = await WtmBlazor.Api.CallAPI("/api/ProductInboundBill/AddWithLotAndRoll", HttpMethodEnum.POST, Model);
        // if (rv.StatusCode == System.Net.HttpStatusCode.OK)
        // {
        //     await WtmBlazor.Toast.Success(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Success"]);
        //
        //     CloseDialog(DialogResult.Yes);
        //     s = true;
        // }
        // else
        // {
        //     if (rv.Errors == null)
        //     {
        //         await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], rv.StatusCode.ToString());
        //     }
        //     else
        //     {
        //         //当自定义组件的验证失效时,可以增加自己的验证提示消息
        //
        //         //string errorMsg = "";
        //         //foreach (var key in rv.Errors.Form.Keys)
        //         // {
        //         //     errorMsg += key + "：" + rv.Errors.Form[key] + "\n" + System.Environment.NewLine;
        //         // }
        //
        //         foreach (var key in rv.Errors.Message)
        //         {
        //             await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], key);
        //         }
        //             
        //         //await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], rv.Errors.GetFirstError());
        //
        //     }
        // }
    }

    //订单选择后查询产品名称和规格
    private async Task OnSelectedChanged(PurchaseOrder_View item)
    {
        if (item is not null)
        {
            await ProductOnSelectPOrder(item.ID.ToString());
            //await WtmBlazor.Toast.Warning("订单变化警告:", "订单改变后,请重新选择颜色,否则为默认颜色", true, false);
        }
    }


    //根据订单ID查询产品名称和规格
    public async Task ProductOnSelectPOrder(string id)
    {
        Model.Entity.POrderId = Guid.Parse(id);
        var rv = await WtmBlazor.Api.CallAPI<PurchaseOrderVM>($"/api/Models/PurchaseOrder/{id}");
        var p = rv.Data;
        product = p.Entity.Product;
        spec = "";
        if (product.GSM != 0)
        {
            spec += product.GSM + "Gsm";
        }
        if (product.Width != 0)
        {
            spec += " - " + product.Width + "CM";
        }
        //await SearchOrderDetail();
        StateHasChanged();
    }

    //配缸记录选择弹窗
    private List<LotAllocate_View> lotdetail { get; set; } = new();
    public async Task SelectLot()
    {
        var result = await WtmBlazor.Dialog.ShowModal<DialogSelectLot>(new ResultDialogOption()
        {
            Title = "请选择缸号",
            ButtonYesText = "确定",
            Size = Size.Large,
            //HeaderTemplate="",
            ShowHeaderCloseButton = false,//弹窗右上角关闭取消
            ButtonYesIcon = "fa-solid fa-magnifying-glass",
            ComponentParameters = new Dictionary<string, object>
            {
                [nameof(DialogSelectLot.OrderId)] = Model.Entity.POrderId.ToString(),
                [nameof(DialogSelectLot.SelectedDetail)] = lotdetail,
                [nameof(DialogSelectLot.SelectedDetailChanged)] = EventCallback.Factory.Create<List<LotAllocate_View>>(this, v => lotdetail = v)
            }
        });
        ProductInboundLot detail = new();
        if (result == DialogResult.Yes)
        {
            foreach (var item in lotdetail)
            {
                detail.OrderDetailId = item.ColorId;
                detail.Color = item.Color;
                detail.LotNo = item.LotNo;
                detail.Pcs = item.Pcs;
                //detail.Meters = item.Meters;
                //detail.Weight = item.Weight;
                Model.Entity.LotList.Insert(Model.Entity.LotList.Count, detail);
            }
            await Task.CompletedTask;
        }
    }


    // 自定义提交处理方法，支持异步操作
    private async Task<bool> SubmitWithCustomHandling()
    {
        try
        {
            // 设置删除的文件ID
            if (Model is BaseVM bv)
            {
                bv.DeletedFileIds = this.DeletedFileIds;
            }

            // 调用API
            var rv = await WtmBlazor.Api.CallAPI("/api/ProductInboundBill/AddWithLotAndRoll", HttpMethodEnum.POST, Model);

            if (rv.StatusCode == System.Net.HttpStatusCode.OK)
            {
                // 提交成功后的处理
                await WtmBlazor.Toast.Success(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.OprationSuccess"]);

                if (OnCloseDialog != null)
                {
                    // Dialog模式：调用CloseDialog
                    CloseDialog(DialogResult.Yes);
                }
                else
                {
                    // Navigation模式：直接导航回列表页面
                    Navigation.NavigateTo("/Finished/ProductInboundBill");
                    await TabSet?.CloseCurrentTab();
                }

                return true;
            }
            else
            {
                // 处理错误
                if (rv.Errors != null)
                {
                    if (rv.Errors.Message != null && rv.Errors.Message.Count > 0)
                    {
                        foreach (var key in rv.Errors.Message)
                        {
                            await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], key);
                        }
                    }

                    // 设置表单验证错误
                    SetError(vform, rv.Errors);
                }
                else
                {
                    await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], rv.StatusCode.ToString());
                }

                return false;
            }
        }
        catch (Exception ex)
        {
            await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], ex.Message);
            return false;
        }

    }

    public async Task OnClose()
    {
       
        // 防止重复调用
        if (isClosing)
        {
            Console.WriteLine("OnClose已在执行中，跳过重复调用");
            return;
        }

        try
        {
            isClosing = true;
            Console.WriteLine("OnClose方法被调用");

            // 根据模式选择关闭方式
            if (OnCloseDialog != null)
            {
                Console.WriteLine("检测到Dialog模式，调用CloseDialog");
                // Dialog模式：调用CloseDialog
                CloseDialog();
            }
            else
            {
                Console.WriteLine("检测到Navigation模式，直接导航回列表页面");

                // Navigation模式：直接导航回列表页面
                // 这是最可靠的方法，虽然不会关闭Tab，但会切换到列表页面
                Navigation.NavigateTo("/Finished/ProductInboundBill");
                await TabSet.CloseCurrentTab();
                Console.WriteLine("已导航回列表页面");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"OnClose方法执行出错: {ex.Message}");
            // 出错时降级为导航方式
            Navigation.NavigateTo("/Finished/ProductInboundBill");
        }
        finally
        {
            // 延迟重置标志，防止过快的重复调用
            await Task.Delay(1000);
            isClosing = false;
        }
    }

    // 尝试通过Blazor组件直接关闭Tab
    private async Task<bool> TryCloseTabDirectly()
    {
        try
        {
            Console.WriteLine("尝试通过Blazor组件直接关闭Tab");

            // 方法1: 尝试通过父组件关闭


            // 方法2: 尝试通过JSRuntime调用BootstrapBlazor的Tab关闭API
            await JSRuntime.InvokeVoidAsync("eval", @"
                // 查找BootstrapBlazor Tab组件
                var tabComponent = document.querySelector('.tabs, .bb-tabs, [data-bb-tab]');
                if (tabComponent && tabComponent.blazorRef) {
                    // 调用Blazor组件的关闭方法
                    tabComponent.blazorRef.invokeMethodAsync('CloseCurrentTab');
                }
            ");

            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"直接关闭Tab失败: {ex.Message}");
            return false;
        }
    }

    // 调试Tab结构的方法
    public async Task DebugTabStructure()
    {
        try
        {
            Console.WriteLine("=== 开始调试Tab结构 ===");

            // 调用JavaScript调试函数
            await JSRuntime.InvokeVoidAsync("tabFuncs.debugTabs");

            // 测试关闭Tab功能
            Console.WriteLine("测试关闭Tab功能...");
            var result = await JSRuntime.InvokeAsync<bool>("tabFuncs.closeCurrentTab");
            Console.WriteLine($"关闭Tab测试结果: {result}");

            // 显示Toast通知
            await WtmBlazor.Toast.Information("调试信息", $"Tab关闭测试结果: {result}，请查看浏览器控制台获取详细信息");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"调试Tab结构时出错: {ex.Message}");
            await WtmBlazor.Toast.Error("调试错误", ex.Message);
        }
    }

}

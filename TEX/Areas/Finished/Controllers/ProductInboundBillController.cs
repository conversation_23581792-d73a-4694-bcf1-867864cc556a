//using EFCore.BulkExtensions; //需要更改连接字符串和Mysql数据库配置文件,麻烦

using Elsa.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TEX.DataAccess;
using TEX.Model.Finished;
using TEX.Model.Models;
using TEX.ViewModel.Finished.ProductInboundBillVMs;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using WalkingTec.Mvvm.Mvc;


namespace TEX.Controllers
{
    [Area("Finished")]
    [AuthorizeJwtWithCookie]
    [ActionDescription("成品入库单")]
    [ApiController]
    [Route("api/ProductInboundBill")]
    public partial class ProductInboundBillController : BaseApiController
    {
        [ActionDescription("Sys.Search")]
        [HttpPost("Search")]
        public IActionResult Search(ProductInboundBillSearcher searcher)
        {
            if (ModelState.IsValid)
            {
                var vm = Wtm.CreateVM<ProductInboundBillListVM>();
                vm.Searcher = searcher;
                return Content(vm.GetJson(enumToString: false));
            }
            else
            {
                return BadRequest(ModelState.GetErrorJson());
            }
        }

        [ActionDescription("Sys.Get")]
        [HttpGet("{id}")]
        public ProductInboundBillVM Get(string id)
        {
            var vm = Wtm.CreateVM<ProductInboundBillVM>(id);
            return vm;
        }

        [ActionDescription("Sys.Create")]
        [HttpPost("Add")]
        public IActionResult Add(ProductInboundBillVM vm)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                vm.DoAdd();
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState.GetErrorJson());
                }
                else
                {
                    return Ok(vm.Entity);
                }
            }
        }

        [ActionDescription("Sys.Edit")]
        [HttpPut("Edit")]
        public IActionResult Edit(ProductInboundBillVM vm)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                vm.DoEdit(false);
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState.GetErrorJson());
                }
                else
                {
                    return Ok(vm.Entity);
                }
            }
        }

        /// <summary>
        /// 使用EFcore添加入库单(三级联动) - AddWithLotAndRoll API
        /// </summary>
        /// <param name="vm"></param>
        /// <returns></returns>
        [HttpPost("AddWithLotAndRoll")]
        public async Task<IActionResult> AddWithLotAndRoll(ProductInboundBillVM vm)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }

            // {{ AURA-X: Fix - 检测数据库类型，在内存数据库中跳过事务以避免测试项目FakeNestedTransaction异常. Source: Microsoft官方文档 }}
            var isInMemoryDatabase = Wtm.DC.Database.ProviderName?.Contains("InMemory") == true;
            var transaction = isInMemoryDatabase ? null : Wtm.DC.BeginTransaction();

            try
            {
                var bill = vm.Entity;
                var tenantCode = Wtm.LoginUserInfo?.CurrentTenant;
                var usercode = Wtm.LoginUserInfo?.ITCode;

                // 验证业务数据
                if (bill.LotList == null)
                {
                    bill.LotList = new();
                }

                // {{ AURA-X: Add - 验证批次和卷号数据的唯一性，包括数据库重复检查 }}
                await ValidateLotListAsync(bill);

                if (!ModelState.IsValid)
                {
                    var k = ModelState.Values.First();
                    ErrorObj errorObj=new() { 
                        Message=new(),
                        Form=new()
                    };
                    //var i = 0;//作为Form的key,不能重复
                    foreach (var er in k.Errors)
                    {
                        errorObj.Message.Add( er.ErrorMessage);
                        //errorObj.Form.Add(i.ToString(), er.ErrorMessage);//和Message重复
                        //i++;
                    }
                    //var e = ModelState.GetErrorJson();//只能显示第一个错误
                    return BadRequest(errorObj);
                }

                // 设置租户信息
                bill.TenantCode = tenantCode;
                bill.CreateBy = usercode;
                bill.CreateTime = DateTime.Now;

                if (bill.LotList.Any())
                {
                    bill.Pcs = bill.LotList.Sum(x => x.Pcs);
                    bill.Meters = bill.LotList.Sum(x => x.Meters);
                    bill.Weight = bill.LotList.Sum(x => x.Weight);
                    bill.Yards = bill.LotList.Sum(x => x.Yards);
                    // 为Lot和Roll设置关联关系和租户信息
                    foreach (var lot in bill.LotList)
                    {
                        // 验证OrderDetailId
                        if (lot.OrderDetailId == Guid.Empty)
                        {
                            return BadRequest($"批次 {lot.LotNo} 的订单明细ID不能为空");
                        }

                        lot.InboundBillId = bill.ID;
                        lot.CreateTime = DateTime.Now;
                        lot.TenantCode = tenantCode;
                        lot.CreateBy = usercode;


                        if (lot.RollList != null && lot.RollList.Any())
                        {
                            lot.Pcs = lot.RollList.Count;
                            lot.Meters = lot.RollList.Sum(x => x.Meters);
                            lot.Weight = lot.RollList.Sum(x => x.Weight);
                            lot.Yards = lot.RollList.Sum(x => x.Yards);

                            foreach (var roll in lot.RollList)
                            {
                                roll.LotId = lot.ID;
                                roll.LotNo = lot.LotNo;
                                roll.CreateTime = DateTime.Now;
                                roll.TenantCode = tenantCode;
                                roll.CreateBy = usercode;
                            }
                        }
                    }

                    // 计算库存变更
                    var addStockList = bill.LotList.GroupBy(x => x.OrderDetailId).Select(x => new StockInfo
                    {
                        OrderDetailId = x.Key,
                        TotalPcs = x.Sum(y => y.Pcs),
                        TotalMeters = x.Sum(y => y.Meters),
                        TotalWeight = x.Sum(y => y.Weight),
                        TotalYards = x.Sum(y => y.Yards),
                    }).ToList();


                    // 查询现有库存记录
                    var orderDetailIds = addStockList.Select(r => r.OrderDetailId).ToList();
                    var productStocks = await base.DC.Set<ProductStock>()
                        .Where(s => orderDetailIds.Contains(s.OrderDetailId))
                        .ToDictionaryAsync(s => s.OrderDetailId);

                    // 更新库存
                    foreach (var stockChange in addStockList)
                    {
                        if (!productStocks.TryGetValue(stockChange.OrderDetailId, out var existingStock))
                        {
                            // 新增库存记录
                            var newStock = new ProductStock
                            {
                                OrderDetailId = stockChange.OrderDetailId,
                                TotalPcs = stockChange.TotalPcs,
                                TotalMeters = stockChange.TotalMeters,
                                TotalWeight = stockChange.TotalWeight,
                                TotalYards = stockChange.TotalYards,
                                CreateTime = DateTime.Now,
                                CreateBy = usercode,
                                TenantCode = tenantCode
                            };
                            base.DC.Set<ProductStock>().Add(newStock);
                        }
                        else
                        {
                            // 更新现有库存记录
                            existingStock.TotalPcs += stockChange.TotalPcs;
                            existingStock.TotalWeight += stockChange.TotalWeight;
                            existingStock.TotalMeters += stockChange.TotalMeters;
                            existingStock.TotalYards += stockChange.TotalYards;
                            existingStock.UpdateTime = DateTime.Now;
                            existingStock.UpdateBy = usercode;
                            // 不需要调用Update，EF Core会自动跟踪变更
                        }
                    }
                }

                // 添加入库单
                DC.Set<ProductInboundBill>().Add(bill);

                // 保存所有变更
                await DC.SaveChangesAsync();

                // {{ AURA-X: Fix - 条件性提交事务，内存数据库跳过事务操作. Source: Microsoft官方文档 }}
                if (transaction != null)
                {
                    await transaction.CommitAsync();
                }

                return Ok(bill);
            }
            catch (Exception ex)
            {
                // {{ AURA-X: Fix - 条件性回滚事务，内存数据库跳过事务操作. Source: Microsoft官方文档 }}
                if (transaction != null)
                {
                    transaction.Rollback();
                }
                ModelState.AddModelError("", ex.Message);
                return BadRequest(ModelState.GetErrorJson());
            }
        }

        /// <summary>
        /// 三级联动修改 - 利用EF Core 8实体跟踪机制实现优雅更新
        /// </summary>
        [HttpPut("EditWithLotAndRoll")]
        public async Task<IActionResult> EditWithLotAndRoll(ProductInboundBillVM vm)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }

            // {{ AURA-X: Fix - 检测数据库类型，在内存数据库中跳过事务以避免FakeNestedTransaction异常. Source: Microsoft官方文档 }}
            var isInMemoryDatabase = Wtm.DC.Database.ProviderName?.Contains("InMemory") == true;
            var transaction = isInMemoryDatabase ? null : Wtm.DC.BeginTransaction();

            try
            {
                var bill = vm.Entity;
                var tenantCode = Wtm.LoginUserInfo?.CurrentTenant;
                var userCode = Wtm.LoginUserInfo?.ITCode;

                if (bill.LotList is null)
                {
                    bill.LotList = [];
                }

                // {{ AURA-X: Add - 验证批次和卷号数据的唯一性 }}
                await ValidateLotListAsync(bill);
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState.GetErrorJson());
                }

                // 1. 更新现有实体的Bill和Lot的统计信息和基本信息

                foreach (var lot in bill.LotList)
                {
                    lot.RollList = lot.RollList ?? [];
                    // 1.1 更新现有Lot的统计信息
                    if (lot.RollList.Any())
                    {
                        lot.Pcs = lot.RollList.Count;
                        lot.Meters = lot.RollList.Sum(x => x.Meters);
                        lot.Weight = lot.RollList.Sum(x => x.Weight);
                        lot.Yards = lot.RollList.Sum(x => x.Yards);
                    }
                }

                // 1.2 更新现有Bill的统计信息
                if (bill.LotList.Any())
                {
                    bill.Pcs = bill.LotList.Sum(x => x.Pcs);
                    bill.Meters = bill.LotList.Sum(x => x.Meters);
                    bill.Weight = bill.LotList.Sum(x => x.Weight);
                    bill.Yards = bill.LotList.Sum(x => x.Yards);
                }

                // 2. 清理ChangeTracker避免实体跟踪冲突
                DataContext dc = (DataContext)DC;
                dc.ChangeTracker.Clear();

                // 3. 查询数据库中现有的完整实体（包含三级关联）
                var existingBill = await DC.Set<ProductInboundBill>()
                    .Include(x => x.LotList)
                    .ThenInclude(x => x.RollList)
                    .FirstOrDefaultAsync(x => x.ID == bill.ID);

                // {{ AURA-X: Add - 计算原始库存统计，用于后续库存差异计算. Confirmed via 寸止 }}
                var originalStockList = existingBill.LotList
                    .Where(x => x.IsValid)
                    .GroupBy(x => x.OrderDetailId)
                    .Select(x => new StockInfo
                    {
                        OrderDetailId = x.Key,
                        TotalPcs = x.Sum(y => y.Pcs),
                        TotalMeters = x.Sum(y => y.Meters),
                        TotalWeight = x.Sum(y => y.Weight),
                        TotalYards = x.Sum(y => y.Yards),
                    }).ToList();

                if (existingBill == null) return NotFound($"ProductInboundBill with ID {bill.ID} not found.");

                // 4. 分离数据操作：新增、更新、删除
                var newLotList = new List<ProductInboundLot>();
                var updateLotList = new List<ProductInboundLot>();
                var deleteLotList = new List<ProductInboundLot>();
                var newRollList = new List<ProductInboundRoll>();
                var updateRollList = new List<ProductInboundRoll>();
                var deleteRollList = new List<ProductInboundRoll>();


                // 4.1 处理传入的Lot数据
                foreach (var inputLot in bill.LotList)
                {
                    var existingLot = existingBill.LotList.FirstOrDefault(x => x.ID == inputLot.ID);
                    if (existingLot == null)
                    {
                        // 新增Lot
                        inputLot.InboundBillId = bill.ID;
                        inputLot.CreateTime = DateTime.Now;
                        inputLot.CreateBy = userCode;
                        inputLot.TenantCode = tenantCode;
                        inputLot.IsValid = true;
                        newLotList.Add(inputLot);

                        // 处理新Lot下的Roll
                        if (inputLot.RollList != null)
                        {
                            foreach (var roll in inputLot.RollList)
                            {
                                roll.LotId = inputLot.ID;
                                roll.LotNo = inputLot.LotNo;
                                roll.CreateTime = DateTime.Now;
                                roll.CreateBy = userCode;
                                roll.TenantCode = tenantCode;
                                roll.IsValid = true;
                                newRollList.Add(roll);
                            }
                        }
                    }
                    else
                    {
                        // 更新现有Lot
                        existingLot.LotNo = inputLot.LotNo;
                        existingLot.OrderDetail = inputLot.OrderDetail;
                        existingLot.Location = inputLot.Location;
                        existingLot.Remark = inputLot.Remark;
                        existingLot.InboundStatus = inputLot.InboundStatus;
                        existingLot.TenantCode = tenantCode;
                        existingLot.Color = inputLot.Color;
                        existingLot.ColorCode = inputLot.ColorCode;


                        existingLot.Pcs = inputLot.Pcs;
                        existingLot.Meters = inputLot.Meters;
                        existingLot.Weight = inputLot.Weight;
                        existingLot.Yards = inputLot.Yards;
                        existingLot.UpdateTime = DateTime.Now;
                        existingLot.UpdateBy = userCode;
                        updateLotList.Add(existingLot);

                        // 处理Roll数据
                        var inputRolls = inputLot.RollList ?? [];
                        foreach (var inputRoll in inputRolls)
                        {
                            var existingRoll = existingLot.RollList.FirstOrDefault(x => x.ID == inputRoll.ID);
                            if (existingRoll == null)
                            {
                                // 新增Roll
                                inputRoll.LotId = existingLot.ID;
                                inputRoll.LotNo = existingLot.LotNo;
                                inputRoll.CreateTime = DateTime.Now;
                                inputRoll.CreateBy = userCode;
                                inputRoll.TenantCode = tenantCode;
                                inputRoll.IsValid = true;
                                newRollList.Add(inputRoll);
                            }
                            else
                            {
                                // 更新现有Roll
                                existingRoll.Grade = inputRoll.Grade;
                                existingRoll.RollNo = inputRoll.RollNo;
                                existingRoll.LotNo = existingLot.LotNo;
                                existingRoll.TenantCode = tenantCode;
                                existingRoll.Remark = inputRoll.Remark;
                                existingRoll.Meters = inputRoll.Meters;
                                existingRoll.Weight = inputRoll.Weight;
                                existingRoll.Yards = inputRoll.Yards;
                                existingRoll.Remark = inputRoll.Remark;
                                existingRoll.UpdateTime = DateTime.Now;
                                existingRoll.UpdateBy = userCode;
                                updateRollList.Add(existingRoll);
                            }
                        }

                        // 找出需要删除的Roll（软删除）
                        var rollsToDelete = existingLot.RollList.Where(x => inputRolls.All(y => y.ID != x.ID)).ToList();
                        foreach (var roll in rollsToDelete)
                        {
                            roll.IsValid = false;
                            roll.UpdateTime = DateTime.Now;
                            roll.UpdateBy = userCode;
                            deleteRollList.Add(roll);
                        }
                    }
                }

                // 4.2 找出需要删除的Lot（软删除）
                deleteLotList = existingBill.LotList.Where(x => bill.LotList.All(y => y.ID != x.ID)).ToList();
                foreach (var lot in deleteLotList)
                {
                    lot.IsValid = false;
                    lot.UpdateTime = DateTime.Now;
                    lot.UpdateBy = userCode;

                    // 同时软删除该Lot下的所有Roll
                    foreach (var roll in lot.RollList)
                    {
                        roll.IsValid = false;
                        roll.UpdateTime = DateTime.Now;
                        roll.UpdateBy = userCode;
                        deleteRollList.Add(roll);
                    }
                }

                // 合并删除列表到更新列表
                updateLotList.AddRange(deleteLotList);
                updateRollList.AddRange(deleteRollList);

                // 5. 使用EF Core原生批量操作，避免实体跟踪冲突
                // 批量添加新Lot
                if (newLotList.Any())
                {
                    dc.Set<ProductInboundLot>().AddRange(newLotList);
                }

                // 批量添加新Roll
                if (newRollList.Any())
                {
                    dc.Set<ProductInboundRoll>().AddRange(newRollList);
                }

                // 批量更新Lot（这些实体已经被跟踪，EF Core会自动检测更改）
                // updateLotList中的实体来自existingBill，已经被跟踪，无需调用UpdateRange

                // 批量更新Roll（这些实体已经被跟踪，EF Core会自动检测更改）
                // updateRollList中的实体来自existingBill，已经被跟踪，无需调用UpdateRange

                // 更新Bill主表（existingBill已经被跟踪）
                existingBill.BillNo = bill.BillNo;
                existingBill.CreateDate = bill.CreateDate;
                existingBill.FinishingFactoryId = bill.FinishingFactoryId;
                existingBill.POrderId = bill.POrderId;
                existingBill.Warehouse = bill.Warehouse;
                existingBill.TenantCode = tenantCode;
                existingBill.Remark = bill.Remark;
                existingBill.Pcs = bill.Pcs;
                existingBill.Meters = bill.Meters;
                existingBill.Weight = bill.Weight;
                existingBill.Yards = bill.Yards;
                existingBill.UpdateTime = DateTime.Now;
                existingBill.UpdateBy = userCode;

                // {{ AURA-X: Add - 计算修改后的库存统计和库存差异，更新ProductStock表. Confirmed via 寸止 }}
                // 计算修改后的库存统计
                var currentStockList = bill.LotList
                    .Where(x => x.IsValid)
                    .GroupBy(x => x.OrderDetailId)
                    .Select(x => new StockInfo
                    {
                        OrderDetailId = x.Key,
                        TotalPcs = x.Sum(y => y.Pcs),
                        TotalMeters = x.Sum(y => y.Meters),
                        TotalWeight = x.Sum(y => y.Weight),
                        TotalYards = x.Sum(y => y.Yards),
                    }).ToList();

                // 计算库存差异
                var stockDifferences = CalculateDifferences(originalStockList, currentStockList);

                // 更新ProductStock表
                if (stockDifferences.Any())
                {
                    var orderDetailIds = stockDifferences.Select(r => r.OrderDetailId).ToList();
                    var productStocks = await DC.Set<ProductStock>()
                        .Where(s => orderDetailIds.Contains(s.OrderDetailId))
                        .ToDictionaryAsync(s => s.OrderDetailId);

                    foreach (var stockChange in stockDifferences)
                    {
                        // 跳过没有变化的记录
                        if (stockChange.TotalPcs == 0 && stockChange.TotalMeters == 0 &&
                            stockChange.TotalWeight == 0 && stockChange.TotalYards == 0)
                        {
                            continue;
                        }

                        if (!productStocks.TryGetValue(stockChange.OrderDetailId, out var existingStock))
                        {
                            // 新增库存记录
                            var newStock = new ProductStock
                            {
                                OrderDetailId = stockChange.OrderDetailId,
                                TotalPcs = stockChange.TotalPcs,
                                TotalMeters = stockChange.TotalMeters,
                                TotalWeight = stockChange.TotalWeight,
                                TotalYards = stockChange.TotalYards,
                                CreateTime = DateTime.Now,
                                CreateBy = userCode,
                                TenantCode = tenantCode
                            };
                            DC.Set<ProductStock>().Add(newStock);
                        }
                        else
                        {
                            // 更新现有库存记录
                            existingStock.TotalPcs += stockChange.TotalPcs;
                            existingStock.TotalWeight += stockChange.TotalWeight;
                            existingStock.TotalMeters += stockChange.TotalMeters;
                            existingStock.TotalYards += stockChange.TotalYards;
                            existingStock.UpdateTime = DateTime.Now;
                            existingStock.UpdateBy = userCode;
                            // 不需要调用Update，EF Core会自动跟踪变更
                        }
                    }
                }

                // 统一保存所有更改
                await dc.SaveChangesAsync();

                // {{ AURA-X: Fix - 条件性提交事务，内存数据库跳过事务操作. Source: Microsoft官方文档 }}
                if (transaction != null)
                {
                    transaction.Commit();
                }
                return Ok(bill);
            }
            catch (Exception ex)
            {
                // {{ AURA-X: Fix - 条件性回滚事务，内存数据库跳过事务操作. Source: Microsoft官方文档 }}
                if (transaction != null)
                {
                    transaction.Rollback();
                }
                ModelState.AddModelError("", ex.Message);
                return BadRequest(ModelState.GetErrorJson());
            }
        }


        [HttpPost("BatchDelete")]
        [ActionDescription("Sys.Delete")]
        public IActionResult BatchDelete(string[] ids)
        {
            var vm = Wtm.CreateVM<ProductInboundBillBatchVM>();
            if (ids != null && ids.Count() > 0)
            {
                vm.Ids = ids;
            }
            else
            {
                return Ok();
            }

            if (!ModelState.IsValid || !vm.DoBatchDelete())
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                return Ok(ids.Count());
            }
        }


        [ActionDescription("Sys.Export")]
        [HttpPost("ExportExcel")]
        public IActionResult ExportExcel(ProductInboundBillSearcher searcher)
        {
            var vm = Wtm.CreateVM<ProductInboundBillListVM>();
            vm.Searcher = searcher;
            vm.SearcherMode = ListVMSearchModeEnum.Export;
            return vm.GetExportData();
        }

        [ActionDescription("Sys.CheckExport")]
        [HttpPost("ExportExcelByIds")]
        public IActionResult ExportExcelByIds(string[] ids)
        {
            var vm = Wtm.CreateVM<ProductInboundBillListVM>();
            if (ids != null && ids.Count() > 0)
            {
                vm.Ids = new List<string>(ids);
                vm.SearcherMode = ListVMSearchModeEnum.CheckExport;
            }

            return vm.GetExportData();
        }

        [ActionDescription("Sys.DownloadTemplate")]
        [HttpGet("GetExcelTemplate")]
        public IActionResult GetExcelTemplate()
        {
            var vm = Wtm.CreateVM<ProductInboundBillImportVM>();
            var qs = new Dictionary<string, string>();
            foreach (var item in Request.Query.Keys)
            {
                qs.Add(item, Request.Query[item]);
            }

            vm.SetParms(qs);
            var data = vm.GenerateTemplate(out string fileName);
            return File(data, "application/vnd.ms-excel", fileName);
        }

        [ActionDescription("Sys.Import")]
        [HttpPost("Import")]
        public ActionResult Import(ProductInboundBillImportVM vm)
        {
            if (vm != null && (vm.ErrorListVM.EntityList.Count > 0 || !vm.BatchSaveData()))
            {
                return BadRequest(vm.GetErrorJson());
            }
            else
            {
                return Ok(vm?.EntityList?.Count ?? 0);
            }
        }

        /// <summary>
        /// 验证LotList中的数据是否符合业务规则
        /// 1. ProductInboundLot表中InboundBillId+LotNo+OrderDetailId组合全局唯一
        /// 2. ProductInboundRoll表中LotNo+OrderDetailId+RollNo组合全局唯一,LotNo+OrderDetailId需要从Lot分组
        /// </summary>
        /// <param name="bill">入库单</param>

        private async Task ValidateLotListAsync(ProductInboundBill bill)
        {

            List<ProductInboundLot> lotList = bill.LotList;
            if (lotList == null || !lotList.Any())
                return;

            var tenantCode = Wtm.LoginUserInfo?.CurrentTenant;
            var dc = (DataContext)DC;

            // 1. 检查传入数据内部的重复性
            // 每个Lot的LotNo和OrderDetailId组合必须唯一
            //var lotNoOrderDetailIdGroup = lotList.GroupBy(x => new { x.LotNo, x.OrderDetailId });
            //var duplicateGroups = lotNoOrderDetailIdGroup.Where(g => g.Count() > 1).ToList();

            //if (duplicateGroups.Any())
            //{
            //    // 批量查询OrderDetail信息
            //    var duplicateOrderDetailIds = duplicateGroups.Select(g => g.Key.OrderDetailId).Distinct().ToList();
            //    var duplicateOrderDetails = await DC.Set<OrderDetail>()
            //        .Where(x => duplicateOrderDetailIds.Contains(x.ID))
            //        .Select(x => new { x.ID, x.Color })
            //        .ToListAsync();
            //    var duplicateOrderDetailDict = duplicateOrderDetails.ToDictionary(x => x.ID, x => x.Color);

            //    foreach (var group in duplicateGroups)
            //    {
            //        var color = duplicateOrderDetailDict.GetValueOrDefault(group.Key.OrderDetailId, "未知");
            //        ModelState.AddModelError("缸号重复", $"颜色:{color}, 缸号:{group.Key.LotNo}");
            //    }
            //}

            // 按LotNo+OrderDetailId分组，验证每个分组中的所有RollNo不重复
            var lotGroups = lotList.GroupBy(x => new { x.LotNo, x.OrderDetailId });
            foreach (var lotGroup in lotGroups)
            {
                // 收集该分组下所有的Roll
                var allRolls = lotGroup.SelectMany(lot => lot.RollList ?? new List<ProductInboundRoll>()).ToList();

                if (allRolls.Any())
                {
                    var rollNoGroup = allRolls.GroupBy(x => x.RollNo);
                    foreach (var rollGroup in rollNoGroup)
                    {
                        if (rollGroup.Count() > 1)
                        {
                            ModelState.AddModelError("卷号重复", $"缸号: {lotGroup.Key.LotNo} 卷号: {rollGroup.Key} 重复");
                        }
                    }
                }
            }

            
            // 2. 检查数据库中的全局唯一性约束
            
            /// ProductInboundLot表不需要验证,应该允许同一个入库单中出现同一个LotNo+OrderDetailId组合,方便分开标记同一缸中可能需要分开的情况
            // 2.1 检查ProductInboundLot表中InboundBillId+LotNo+OrderDetailId组合全局唯一（批量优化）
            // if (lotList.Any())
            // {
            //     // 收集所有需要验证的Lot信息
            //     var lotsToValidate = lotList.Select(x => new {
            //         x.ID,
            //         x.LotNo,
            //         x.OrderDetailId
            //     }).ToList();
            //
            //     // 批量查询数据库中当前入库单的所有Lot记录
            //     var existingLots = await dc.ProductInboundLots
            //         .Where(x => x.InboundBillId == bill.ID &&
            //                    x.IsValid == true &&
            //                    x.TenantCode == tenantCode)
            //         .Select(x => new {
            //             x.ID,
            //             x.LotNo,
            //             x.OrderDetailId
            //         })
            //         .ToListAsync();
            //
            //     // 批量查询OrderDetail信息用于错误提示
            //     var orderDetailIds = lotsToValidate.Select(x => x.OrderDetailId).Distinct().ToList();
            //     var orderDetails = await DC.Set<OrderDetail>()
            //         .Where(x => orderDetailIds.Contains(x.ID))
            //         .Select(x => new { x.ID, x.Color })
            //         .ToListAsync();
            //     var orderDetailDict = orderDetails.ToDictionary(x => x.ID, x => x.Color);
            //
            //     // 在内存中进行验证
            //     foreach (var lotToValidate in lotsToValidate)
            //     {
            //         var conflictingLot = existingLots.FirstOrDefault(x =>
            //             x.LotNo == lotToValidate.LotNo &&
            //             x.OrderDetailId == lotToValidate.OrderDetailId &&
            //             x.ID != lotToValidate.ID); // 排除当前正在编辑的Lot
            //
            //         if (conflictingLot != null)
            //         {
            //             var color = orderDetailDict.GetValueOrDefault(lotToValidate.OrderDetailId, "未知");
            //             ModelState.AddModelError("缸号重复", $"颜色:{color}, 缸号:{lotToValidate.LotNo} 在当前入库单中已存在");
            //         }
            //     }
            // }

            // 2.2 检查ProductInboundRoll表中LotNo+OrderDetailId+RollNo组合全局唯一（批量优化）
            // 收集所有需要验证的Roll信息
            var rollsToValidate = new List<(string LotNo, Guid OrderDetailId, int RollNo, Guid RollId)>();
            foreach (var lot in lotList)
            {
                if (lot.RollList == null || !lot.RollList.Any())
                    continue;

                foreach (var roll in lot.RollList)
                {
                    rollsToValidate.Add((lot.LotNo, lot.OrderDetailId, roll.RollNo, roll.ID));
                }
            }

            if (rollsToValidate.Any())
            {
                // 提取所有相关的LotNo和OrderDetailId
                var lotNos = rollsToValidate.Select(x => x.LotNo).Distinct().ToList();
                var orderDetailIds = rollsToValidate.Select(x => x.OrderDetailId).Distinct().ToList();

                // 批量查询数据库中所有相关的Roll记录（避免复杂的关联查询）
                var existingRolls = await (from roll in dc.ProductInboundRolls
                                          join lot in dc.ProductInboundLots on roll.LotId equals lot.ID
                                          where lotNos.Contains(lot.LotNo) &&
                                                orderDetailIds.Contains(lot.OrderDetailId) &&
                                                roll.IsValid == true &&
                                                roll.TenantCode == tenantCode
                                          select new {
                                              roll.ID,
                                              lot.LotNo,
                                              lot.OrderDetailId,
                                              roll.RollNo
                                          }).ToListAsync();

                // 在内存中进行验证
                foreach (var rollToValidate in rollsToValidate)
                {
                    var conflictingRoll = existingRolls.FirstOrDefault(x =>
                        x.LotNo == rollToValidate.LotNo &&
                        x.OrderDetailId == rollToValidate.OrderDetailId &&
                        x.RollNo == rollToValidate.RollNo &&
                        x.ID != rollToValidate.RollId); // 排除当前正在编辑的Roll

                    if (conflictingRoll != null)
                    {
                        ModelState.AddModelError("卷号重复", $"缸号 {rollToValidate.LotNo} 中卷号 {rollToValidate.RollNo} 已存在，请检查数据");
                    }
                }
            }
        }
        
        
        /// 计算库存差异，用于更新ProductStock表.
        private List<StockInfo> CalculateDifferences(List<StockInfo> originalList, List<StockInfo> currentList)
        {
            var differences = new List<StockInfo>();

            var originalDict = originalList.ToDictionary(x => x.OrderDetailId);
            var currentDict = currentList.ToDictionary(x => x.OrderDetailId);

            foreach (var current in currentList)
            {
                if (originalDict.TryGetValue(current.OrderDetailId, out StockInfo original))
                {
                    differences.Add(new StockInfo
                    {
                        OrderDetailId = current.OrderDetailId,
                        TotalPcs = current.TotalPcs - original.TotalPcs,
                        TotalMeters = current.TotalMeters - original.TotalMeters,
                        TotalWeight = current.TotalWeight - original.TotalWeight,
                        TotalYards = current.TotalYards - original.TotalYards
                    });
                }
                else
                {
                    differences.Add(new StockInfo
                    {
                        OrderDetailId = current.OrderDetailId,
                        TotalPcs = current.TotalPcs,
                        TotalMeters = current.TotalMeters,
                        TotalWeight = current.TotalWeight,
                        TotalYards = current.TotalYards
                    });
                }
            }

            foreach (var original in originalList)
            {
                if (!currentDict.ContainsKey(original.OrderDetailId))
                {
                    differences.Add(new StockInfo
                    {
                        OrderDetailId = original.OrderDetailId,
                        TotalPcs = -original.TotalPcs,
                        TotalMeters = -original.TotalMeters,
                        TotalWeight = -original.TotalWeight,
                        TotalYards = -original.TotalYards
                    });
                }
            }

            return differences;
        }
    }
}